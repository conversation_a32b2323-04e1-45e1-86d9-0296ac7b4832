#!/bin/bash

# LiteLLM OpenAI SDK Test Runner
# This script runs the comprehensive LiteLLM test suite

echo "🚀 LiteLLM OpenAI SDK Test Runner"
echo "=================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "📝 Created .env file from .env.example"
        echo "🔧 Please edit .env file and set your LITELLM_API_KEY"
        echo ""
    else
        echo "❌ .env.example not found. Please create .env file manually with:"
        echo "   LITELLM_API_KEY=sk-your-persona-key-here"
        echo "   LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com"
        exit 1
    fi
fi

# Check if API key is set
source .env
if [ -z "$LITELLM_API_KEY" ] || [ "$LITELLM_API_KEY" = "sk-your-persona-key-here" ]; then
    echo "❌ LITELLM_API_KEY not set in .env file"
    echo "🔧 Please edit .env file and set your actual API key"
    echo "   Example: LITELLM_API_KEY=sk-your-actual-key"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js first."
    exit 1
fi

# Check if OpenAI package is installed
if [ ! -d "node_modules/openai" ]; then
    echo "📦 Installing OpenAI package..."
    npm install openai
fi

# Check if dotenv package is installed
if [ ! -d "node_modules/dotenv" ]; then
    echo "📦 Installing dotenv package..."
    npm install dotenv
fi

echo "✅ Environment check complete"
echo "🧪 Running LiteLLM OpenAI SDK tests..."
echo ""

# Run the test
node test_litellm_openai_sdk.js

# Capture exit code
exit_code=$?

echo ""
if [ $exit_code -eq 0 ]; then
    echo "🎉 All tests completed successfully!"
    echo ""
    echo "💡 Next steps:"
    echo "   1. Check the full documentation: docs/LITELLM_INTEGRATION_GUIDE.md"
    echo "   2. Try the quick start examples: node examples/litellm_quick_start.js"
    echo "   3. Integrate LiteLLM into your services"
else
    echo "❌ Some tests failed. Please check the output above."
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   1. Verify your API key is correct"
    echo "   2. Check internet connectivity"
    echo "   3. Ensure gateway is accessible: curl https://ai.gateway.equalcollective.com/health"
fi

exit $exit_code
