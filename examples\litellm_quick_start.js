#!/usr/bin/env node

/**
 * LiteLLM Quick Start Guide
 * 
 * This file demonstrates the most common integration patterns for LiteLLM gateway.
 * Run with: node examples/litellm_quick_start.js
 */

require('dotenv').config();
const OpenAI = require('openai');

// Configuration
const GATEWAY_URL = 'https://ai.gateway.equalcollective.com';
const API_KEY = process.env.LITELLM_API_KEY || 'sk-your-key-here';

// Available models for this API key
const AVAILABLE_MODELS = ['gpt-4o', 'gemini/gemini-2.5-flash', 'gemini/gemini-2.5-pro'];
const DEFAULT_MODEL = 'gpt-4o';

console.log('🚀 LiteLLM Quick Start Guide');
console.log(`📍 Gateway: ${GATEWAY_URL}`);
console.log(`🔑 API Key: ${API_KEY ? API_KEY.substring(0, 10) + '...' : 'NOT SET'}\n`);

// Example 1: Basic OpenAI SDK Usage
async function basicExample() {
    console.log('📝 Example 1: Basic OpenAI SDK Usage');
    
    const client = new OpenAI({
        apiKey: API_KEY,
        baseURL: `${GATEWAY_URL}/v1`
    });

    try {
        const completion = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                { role: 'user', content: 'Say hello from LiteLLM!' }
            ],
            max_tokens: 50
        });

        console.log('✅ Response:', completion.choices[0].message.content);
        console.log('📊 Usage:', completion.usage);
    } catch (error) {
        console.log('❌ Error:', error.message);
    }
    console.log('');
}

// Example 2: Using Tags for Cost Tracking
async function tagsExample() {
    console.log('📝 Example 2: Using Tags for Cost Tracking');
    
    const client = new OpenAI({
        apiKey: API_KEY,
        baseURL: `${GATEWAY_URL}/v1`
    });

    try {
        const completion = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                { role: 'user', content: 'Explain what tags are used for' }
            ],
            user: 'quickstart_user',
            metadata: {
                tags: [
                    'service:QuickStart',
                    'function:tagsExample',
                    'useCase:demonstration',
                    'environment:development',
                    'priority:normal'
                ]
            },
            max_tokens: 100
        });

        console.log('✅ Response:', completion.choices[0].message.content);
        console.log('🏷️  Tags sent successfully for cost tracking');
    } catch (error) {
        console.log('❌ Error:', error.message);
    }
    console.log('');
}

// Example 3: Streaming Response
async function streamingExample() {
    console.log('📝 Example 3: Streaming Response');
    
    const client = new OpenAI({
        apiKey: API_KEY,
        baseURL: `${GATEWAY_URL}/v1`
    });

    try {
        const stream = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                { role: 'user', content: 'Count from 1 to 5, each number on a new line' }
            ],
            stream: true,
            max_tokens: 50
        });

        console.log('✅ Streaming response:');
        let fullResponse = '';
        
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
                process.stdout.write(content);
                fullResponse += content;
            }
        }
        
        console.log('\n📊 Stream completed, total length:', fullResponse.length);
    } catch (error) {
        console.log('❌ Error:', error.message);
    }
    console.log('');
}

// Example 4: Multiple Models
async function multipleModelsExample() {
    console.log('📝 Example 4: Testing Multiple Models');
    
    const client = new OpenAI({
        apiKey: API_KEY,
        baseURL: `${GATEWAY_URL}/v1`
    });

    const models = AVAILABLE_MODELS;
    
    for (const model of models) {
        try {
            const completion = await client.chat.completions.create({
                model: model,
                messages: [
                    { role: 'user', content: `Say hello from ${model}` }
                ],
                max_tokens: 20
            });

            console.log(`✅ ${model}: ${completion.choices[0].message.content}`);
        } catch (error) {
            console.log(`❌ ${model}: ${error.message}`);
        }
    }
    console.log('');
}

// Example 5: Service Class Pattern
class LiteLLMService {
    constructor() {
        this.client = new OpenAI({
            apiKey: API_KEY,
            baseURL: `${GATEWAY_URL}/v1`
        });
    }

    async chat(messages, options = {}) {
        const requestBody = {
            model: options.model || DEFAULT_MODEL,
            messages: messages,
            max_tokens: options.maxTokens || 1000,
            temperature: options.temperature || 0.7,
            user: options.user || 'default_user',
            metadata: {
                tags: [
                    `service:${options.service || 'LiteLLMService'}`,
                    `function:${options.functionName || 'chat'}`,
                    `useCase:${options.useCase || 'general'}`,
                    `environment:${process.env.NODE_ENV || 'development'}`,
                    ...options.customTags || []
                ]
            }
        };

        try {
            const completion = await this.client.chat.completions.create(requestBody);
            return {
                success: true,
                message: completion.choices[0].message.content,
                usage: completion.usage,
                model: completion.model
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

async function serviceClassExample() {
    console.log('📝 Example 5: Service Class Pattern');
    
    const service = new LiteLLMService();
    
    const result = await service.chat([
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'What is the benefit of using a service class?' }
    ], {
        service: 'QuickStart',
        functionName: 'serviceClassExample',
        useCase: 'demonstration',
        customTags: ['pattern:service_class', 'priority:high']
    });

    if (result.success) {
        console.log('✅ Response:', result.message);
        console.log('📊 Usage:', result.usage);
        console.log('🤖 Model:', result.model);
    } else {
        console.log('❌ Error:', result.error);
    }
    console.log('');
}

// Example 6: Health Check
async function healthCheckExample() {
    console.log('📝 Example 6: Health Check');
    
    try {
        // Check gateway health
        const healthResponse = await fetch(`${GATEWAY_URL}/health`);
        console.log('✅ Gateway Health:', healthResponse.status === 200 ? 'Healthy' : 'Unhealthy');
        
        // Check models endpoint
        const client = new OpenAI({
            apiKey: API_KEY,
            baseURL: `${GATEWAY_URL}/v1`
        });
        
        const models = await client.models.list();
        console.log('✅ Available Models:', models.data.length);
        console.log('📋 Sample Models:', models.data.slice(0, 3).map(m => m.id).join(', '));
        console.log('🎯 Available Models for your key:', AVAILABLE_MODELS.join(', '));
        
    } catch (error) {
        console.log('❌ Health Check Failed:', error.message);
    }
    console.log('');
}

// Main function to run all examples
async function main() {
    if (!API_KEY || API_KEY === 'sk-your-key-here') {
        console.log('⚠️  Please set LITELLM_API_KEY environment variable');
        console.log('   Example: export LITELLM_API_KEY=sk-your-actual-key');
        return;
    }

    try {
        await healthCheckExample();
        await basicExample();
        await tagsExample();
        await streamingExample();
        await multipleModelsExample();
        await serviceClassExample();
        
        console.log('🎉 All examples completed successfully!');
        console.log('\n💡 Next Steps:');
        console.log('1. Check the full documentation: docs/LITELLM_INTEGRATION_GUIDE.md');
        console.log('2. Run the test suite: node test_litellm_proxy.js');
        console.log('3. Integrate LiteLLM into your services');
        console.log('4. Set up proper tagging for cost tracking');
        
    } catch (error) {
        console.error('❌ Examples failed:', error);
    }
}

// Run examples if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    LiteLLMService,
    basicExample,
    tagsExample,
    streamingExample,
    multipleModelsExample,
    serviceClassExample,
    healthCheckExample
};
