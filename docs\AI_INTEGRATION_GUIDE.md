# AI Integration Guide

This guide explains how to integrate LiteLLM gateway throughout the SellerBot application for unified AI service access.

## Overview

The application uses **LiteLLM Gateway** for unified access to multiple AI providers through a single OpenAI-compatible API.

**Gateway URL**: `https://ai.gateway.equalcollective.com/`

## Architecture

```
Application Code
       ↓
LiteLLM Gateway
       ↓
Multiple AI Providers
(OpenAI, Anthropic, Azure, etc.)
```

## Key Benefits

- **Unified API**: Single interface for all AI providers
- **Cost Tracking**: Built-in usage monitoring with tags
- **Load Balancing**: Automatic routing and failover
- **Provider Flexibility**: Easy switching between AI providers

## Key Components

### 1. LiteLLM Service (`services/ai/litellmService.js`)

The main service that handles all AI operations:
- **Unified API access** through LiteLLM gateway
- **Consistent error handling** across the application
- **Performance monitoring** and logging with tags
- **Multi-provider support** with automatic routing

### 2. Updated ScrapeGPT Services

- `services/scrapeGPT/request.js` - Chat completions via LiteLLM
- `services/scrapeGPT/assistant.js` - Assistant API calls
- `services/scrapeGPT/factory.js` - Template-based completions

### 3. AI Metadata Helper (`utils/aiMetadataHelper.js`)

- Request tagging and metadata generation
- Cost tracking and usage analytics
- Request ID generation and session management

## Environment Variables

### Required Variables
```bash
# LiteLLM Gateway Configuration
LITELLM_API_KEY=sk-your-persona-key-here
LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com

# Optional: Default model preference
LITELLM_DEFAULT_MODEL=gpt-4o
```

### Legacy Variables (for backward compatibility)
```bash
# These may still be used by some legacy services
OPENAI_API_KEY=your_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_DEPLOYMENT=gpt-4o
```

## Usage Examples

### Basic Chat Completion with LiteLLM Service
```javascript
const { litellmService } = require('./services/ai/litellmService');

const messages = [
  { role: 'system', content: 'You are a helpful assistant.' },
  { role: 'user', content: 'Hello!' }
];

const result = await litellmService.createChatCompletion(messages, {
  temperature: 0.7,
  maxTokens: 100,
  model: 'gpt-4o',
  useCase: 'general_chat',
  customTags: ['feature:chat', 'priority:normal']
});

console.log(result.message); // AI response
console.log(result.usage); // Token usage
console.log(result.metadata); // Request metadata and tags
```

### Using ScrapeGPT Services (Updated for LiteLLM)
```javascript
const { getChatGPTResponse } = require('./services/scrapeGPT/request');

const result = await getChatGPTResponse(
  'You are a helpful assistant.',
  'Analyze this text...',
  {
    model: 'gpt-4o',
    useCase: 'text_analysis',
    tags: ['service:scrapeGPT', 'function:getChatGPTResponse']
  }
);
```

### Using Factory Methods with Tags
```javascript
const { completionFactory } = require('./services/scrapeGPT/factory');

const result = await completionFactory('match_text', {
  textContent: 'Company website content...',
  businessKeywords: ['Company Name', 'Product'],
  url: 'https://example.com',
  metadata: {
    tags: ['useCase:text_matching', 'service:SellerBot', 'priority:high']
  }
});
```

### Direct OpenAI SDK Usage with LiteLLM
```javascript
const OpenAI = require('openai');

const client = new OpenAI({
  apiKey: process.env.LITELLM_API_KEY,
  baseURL: 'https://ai.gateway.equalcollective.com/v1'
});

const completion = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    { role: 'user', content: 'Hello from LiteLLM!' }
  ],
  user: 'sellerbot_user',
  metadata: {
    tags: [
      'service:SellerBot',
      'function:directSDKUsage',
      'environment:production'
    ]
  }
});
```

## Testing

### Run LiteLLM Tests
```bash
# Test LiteLLM proxy connectivity and functionality
node test_litellm_proxy.js

# Test LiteLLM with tags and metadata
node test_litellm_tags.js

# Test complete AI integration
node test_complete_ai_integration.js
```

### Test LiteLLM Service
```bash
# Test the LiteLLM service wrapper
node examples/litellm_tags_examples.js

# Run configuration helper
node litellm_proxy_config_helper.js
```

### Test Output Example
```
🧪 LiteLLM Gateway Test
✅ Health Check: PASSED - Status: 200
✅ Models Endpoint: PASSED - Found 15 models
✅ Basic Chat: PASSED - Response: "Hello! How can I help you today?"
✅ Tags Support: PASSED - Metadata sent successfully
✅ Response Time: PASSED - Duration: 1250ms
```

## Health Monitoring

Check the health status of the LiteLLM integration:

```javascript
const { litellmService } = require('./services/ai/litellmService');

const healthStatus = await litellmService.getHealthStatus();
console.log(healthStatus);
```

Output:
```json
{
  "gateway": {
    "status": "healthy",
    "url": "https://ai.gateway.equalcollective.com",
    "response_time": 150
  },
  "models": {
    "available": 15,
    "preferred": "gpt-4o",
    "fallbacks": ["gpt-3.5-turbo", "claude-3-sonnet-20240229"]
  },
  "environment": {
    "hasApiKey": true,
    "hasProxyUrl": true,
    "keyFormat": "valid"
  }
}
```

### Quick Health Check
```javascript
// Simple connectivity test
const testConnection = async () => {
  try {
    const response = await fetch('https://ai.gateway.equalcollective.com/health');
    console.log('Gateway Status:', response.status === 200 ? 'Healthy' : 'Unhealthy');
  } catch (error) {
    console.log('Gateway Status: Unreachable');
  }
};
```

## Benefits

### 1. **Unified Interface**
- Single API for all AI providers (OpenAI, Anthropic, Azure, etc.)
- Drop-in replacement for OpenAI SDK
- Consistent error handling across providers

### 2. **Cost Tracking & Analytics**
- Detailed usage tracking with custom tags
- Cost monitoring per service/feature/user
- Built-in analytics and reporting

### 3. **Reliability & Performance**
- Automatic load balancing across providers
- Built-in failover and retry logic
- Rate limiting and quota management

### 4. **Developer Experience**
- Easy integration with existing OpenAI code
- Comprehensive tagging for debugging
- Real-time monitoring and health checks

## Troubleshooting

### Common Issues

1. **"401 Unauthorized" or "Invalid API key"**
   - Check that `LITELLM_API_KEY` is set correctly
   - Verify the API key starts with 'sk-'
   - Contact administrator for persona-specific key

2. **"Gateway unreachable" or connection errors**
   - Check internet connectivity
   - Verify gateway URL: `https://ai.gateway.equalcollective.com`
   - Test with: `curl https://ai.gateway.equalcollective.com/health`

3. **"Model not found" errors**
   - Check available models: `GET /v1/models`
   - Use fallback models like 'gpt-3.5-turbo'
   - Verify model name spelling

### Debug Mode

Enable detailed logging:
```bash
# Set environment variables for debugging
DEBUG=openai:*
LITELLM_DEBUG=true

# Or use the debug client from the documentation
node -e "
const { DebugLiteLLMClient } = require('./path/to/debug/client');
const client = new DebugLiteLLMClient();
client.debugRequest({
  model: 'gpt-4o',
  messages: [{ role: 'user', content: 'test' }]
});
"
```

## Migration from Legacy Systems

### Migrating from Portkey/Azure Direct

1. **Update environment variables**:
   ```bash
   # Replace these
   PORTKEY_BASE_URL=...
   PORTKEY_API_KEY=...

   # With these
   LITELLM_API_KEY=sk-your-persona-key
   LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com
   ```

2. **Update service imports**:
   ```javascript
   // Old
   const { centralizedAI } = require('./services/ai/centralizedAIService');

   // New
   const { litellmService } = require('./services/ai/litellmService');
   ```

3. **Update function calls**:
   ```javascript
   // Old
   const result = await centralizedAI.createChatCompletion(messages, options, 'azure', true);

   // New
   const result = await litellmService.createChatCompletion(messages, options);
   ```

### Backward Compatibility

- Most existing function signatures work with minimal changes
- Legacy environment variables are still supported
- Gradual migration path available

## Performance

- **Average response time**: ~1-2 seconds
- **Gateway latency**: <100ms additional overhead
- **Cost tracking**: Real-time usage monitoring
- **Rate limiting**: Automatic handling across providers

## Documentation and Resources

### Complete Integration Guide
For detailed integration examples, see: [LITELLM_INTEGRATION_GUIDE.md](./LITELLM_INTEGRATION_GUIDE.md)

### Gateway Documentation
Official documentation: [https://ai.gateway.equalcollective.com/](https://ai.gateway.equalcollective.com/)

### Test Files
- `test_litellm_proxy.js` - Basic connectivity tests
- `test_litellm_tags.js` - Tags and metadata tests
- `examples/litellm_tags_examples.js` - Usage examples

## Next Steps

1. **Run the test suite** to verify connectivity
2. **Update your services** to use LiteLLM
3. **Implement tagging** for cost tracking
4. **Monitor usage** through the gateway dashboard
