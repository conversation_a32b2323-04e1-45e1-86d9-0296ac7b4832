@echo off
REM LiteLLM OpenAI SDK Test Runner (Windows)
REM This script runs the comprehensive LiteLLM test suite

echo 🚀 LiteLLM OpenAI SDK Test Runner
echo ==================================

REM Check if .env file exists
if not exist .env (
    echo ⚠️  .env file not found. Creating from example...
    if exist .env.example (
        copy .env.example .env >nul
        echo 📝 Created .env file from .env.example
        echo 🔧 Please edit .env file and set your LITELLM_API_KEY
        echo.
    ) else (
        echo ❌ .env.example not found. Please create .env file manually with:
        echo    LITELLM_API_KEY=sk-your-persona-key-here
        echo    LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com
        pause
        exit /b 1
    )
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if OpenAI package is installed
if not exist node_modules\openai (
    echo 📦 Installing OpenAI package...
    npm install openai
)

REM Check if dotenv package is installed
if not exist node_modules\dotenv (
    echo 📦 Installing dotenv package...
    npm install dotenv
)

echo ✅ Environment check complete
echo 🧪 Running LiteLLM OpenAI SDK tests...
echo.

REM Run the test
node test_litellm_openai_sdk.js

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Some tests failed. Please check the output above.
    echo.
    echo 🔧 Troubleshooting:
    echo    1. Verify your API key is correct in .env file
    echo    2. Check internet connectivity
    echo    3. Ensure gateway is accessible
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 All tests completed successfully!
    echo.
    echo 💡 Next steps:
    echo    1. Check the full documentation: docs\LITELLM_INTEGRATION_GUIDE.md
    echo    2. Try the quick start examples: node examples\litellm_quick_start.js
    echo    3. Integrate LiteLLM into your services
    pause
)
