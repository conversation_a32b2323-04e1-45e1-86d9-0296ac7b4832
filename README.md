## SellerBot - AI-Powered Amazon Seller Analysis Platform

### AI Integration with LiteLLM Gateway

SellerBot now integrates with LiteLLM gateway for unified AI service access across multiple providers.

**Quick Start**: See [examples/litellm_quick_start.js](examples/litellm_quick_start.js)
**Full Documentation**: [docs/LITELLM_INTEGRATION_GUIDE.md](docs/LITELLM_INTEGRATION_GUIDE.md)
**Gateway URL**: https://ai.gateway.equalcollective.com/

#### Key Features
- 🔄 **Unified API** for OpenAI, Anthropic, Azure, and more
- 🏷️ **Cost Tracking** with custom tags and metadata
- ⚡ **Load Balancing** and automatic failover
- 📊 **Real-time Monitoring** and analytics

## Overview

**SellerBot** is a system designed to manage relationships between sellers and countries using smartScout. The key challenge addressed by this system is ensuring that each seller in a given smartScout country has a unique final, correct domain. The system manages seller-country-domain combinations and enforces rules to maintain data consistency.

## System Overview

- **One smartScout Country** can have many sellers.
- **One seller** can have many smartScout Countries.
- **One seller** cannot have multiple domains.
- **One domain** can have many sellers, but only one final correct domain per smartScout Country.
- The **final correct domain** can have many unique smartScout Country-seller combinations.

## Key Approach

We treat the combination of **smartScout Country** and **seller_id** as the primary key to update the database.

## Use Cases Covered

1. **Push data with seller_id + smartScout Country (without website):**  
   Add or update data without a website field.

2. **Push data with seller_id + smartScout Country + website:**  
   Update the website (domain) for the seller in that smartScout Country.

3. **Update the domain to be the final one:**  
   Ensure that only one final correct domain exists per seller_id + smartScout Country combination.

## Important Notes

- When making any data changes, always provide the **seller_id** and **smartScout Country**.  
  If you fail to provide these, a new row will be created, and you won't be able to update the combined primary key (seller_id + smartScout Country).
- Fields such as `amazon_seller_id` and `smartScout Country` cannot be updated.

# Running TestFindWebsite.js

1. For running non async api just start the file and select import csv and put file path and type n for running it using sync apis
2. For running async api first make sure you have db created you can use `npm run migrate:sqlite` or `npx prisma migrate dev --schema /database/prisma/sqlitePrisma/sqlite.schema.prisma` to create db and then run the file
3. then for running when running the script use y parameter when it asks for async or sync, wait until script schedules all of the api calls
4. if any ratelimit is hit while scheduling you can the `Schedule pending tasks` manually in cli
5. you can run get processable task to get the count of processable tasks in total
6. then you run the `Process pending tasks` to process the tasks that were scheduled and have been scheduled for longer than a minute
7. if rate limit is reached in process pending tasks you can run the `Process pending tasks` again to process the tasks that were scheduled and have been scheduled for longer than a minute

## Achievements

1. A seller can operate in multiple countries, but for each country, they have only **one final website**.
2. All fields can be updated except `amazon_seller_id` and `smartScout Country`.
3. The system enforces the rule: **One nation, one final correct domain per seller**.

## Backup & Restore Commands

- **Backup command:**
  ```bash
  pg_dump -h - -p 5432 -U jeff -d sellers-db --data-only > source_data.sql
  ```
- **Restore command:**

  ```bash
  psql -h localhost -p 5432 -U new_user -d sellers-db < source_data.sql
  ```

- **Copy backup from remote server:**
  ```bash
  scp jeff@**************:'/home/<USER>/hourly_data_backups/*' /Users/<USER>/personal/SellerBot/backups
  ```
