# LiteLLM Integration Guide

Complete guide for integrating LiteLLM with OpenAI SDK, curl requests, and various wrapper approaches.

## Table of Contents

1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [API Key Management](#api-key-management)
4. [Integration Methods](#integration-methods)
5. [Tagging and Metadata](#tagging-and-metadata)
6. [Examples](#examples)
7. [Testing](#testing)
8. [Troubleshooting](#troubleshooting)

## Overview

LiteLLM provides a unified interface to multiple LLM providers through a proxy server. Our gateway is hosted at:

**Base URL**: `https://ai.gateway.equalcollective.com/`

### Supported Integration Methods

- **OpenAI SDK** (JavaScript/Python)
- **Direct HTTP/curl requests**
- **LangGraph wrapper**
- **Custom service wrappers**

## Getting Started

### Prerequisites

1. Access to the LiteLLM gateway
2. Valid API key for your persona/use case
3. OpenAI SDK installed (if using SDK approach)

### Installation

```bash
# For JavaScript/Node.js
npm install openai

# For Python
pip install openai
```

## API Key Management

### Creating Keys for Persona Use

1. **Contact your administrator** to get a persona-specific API key
2. **Key format**: Keys should start with `sk-` followed by your persona identifier
3. **Environment setup**: Store your key securely in environment variables

```bash
# .env file
LITELLM_API_KEY=sk-your-persona-key-here
LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com
```

### Key Security Best Practices

- Never commit API keys to version control
- Use environment variables or secure key management systems
- Rotate keys regularly
- Use different keys for different environments (dev/staging/prod)

## Integration Methods

### 1. OpenAI SDK Integration

#### JavaScript/Node.js

```javascript
const OpenAI = require('openai');

// Initialize client with LiteLLM gateway
const client = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1'
});

// Basic chat completion
async function basicChat() {
    const completion = await client.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            { role: 'system', content: 'You are a helpful assistant.' },
            { role: 'user', content: 'Hello, how are you?' }
        ],
        max_tokens: 100,
        temperature: 0.7
    });
    
    return completion.choices[0].message.content;
}
```

#### Python

```python
from openai import OpenAI
import os

# Initialize client with LiteLLM gateway
client = OpenAI(
    api_key=os.getenv('LITELLM_API_KEY'),
    base_url='https://ai.gateway.equalcollective.com/v1'
)

# Basic chat completion
def basic_chat():
    completion = client.chat.completions.create(
        model='gpt-4o',
        messages=[
            {'role': 'system', 'content': 'You are a helpful assistant.'},
            {'role': 'user', 'content': 'Hello, how are you?'}
        ],
        max_tokens=100,
        temperature=0.7
    )
    
    return completion.choices[0].message.content
```

### 2. Direct HTTP/curl Requests

#### Basic curl Example

```bash
curl -X POST "https://ai.gateway.equalcollective.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LITELLM_API_KEY" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user", 
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 100,
    "temperature": 0.7
  }'
```

#### JavaScript fetch Example

```javascript
async function directHttpRequest() {
    const response = await fetch('https://ai.gateway.equalcollective.com/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.LITELLM_API_KEY}`
        },
        body: JSON.stringify({
            model: 'gpt-4o',
            messages: [
                { role: 'system', content: 'You are a helpful assistant.' },
                { role: 'user', content: 'Hello, how are you?' }
            ],
            max_tokens: 100,
            temperature: 0.7
        })
    });
    
    const data = await response.json();
    return data.choices[0].message.content;
}
```

## Tagging and Metadata

### Why Use Tags?

Tags help with:
- **Cost tracking** by project/feature
- **Usage analytics** and monitoring
- **Debugging** and troubleshooting
- **Performance optimization**

### Tag Format

LiteLLM uses a specific tag format: `["key:value", "key:value"]`

### Basic Tagging Example

```javascript
const requestWithTags = {
    model: 'gpt-4o',
    messages: [
        { role: 'user', content: 'What is the weather like?' }
    ],
    user: 'sellerbot_user', // Optional: track spend by user
    metadata: {
        tags: [
            'jobID:12345',
            'taskName:weather_query',
            'service:SellerBot',
            'function:getWeather',
            'useCase:customer_support',
            'environment:production',
            'requestType:chat_completion'
        ]
    }
};
```

### Advanced Tagging with curl

```bash
curl -X POST "https://ai.gateway.equalcollective.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LITELLM_API_KEY" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "user", "content": "Analyze this data"}
    ],
    "user": "data_analyst_01",
    "metadata": {
      "tags": [
        "jobID:analytics_2024_001",
        "taskName:data_analysis", 
        "service:DataPlatform",
        "function:analyzeDataset",
        "useCase:business_intelligence",
        "environment:production",
        "priority:high",
        "department:analytics"
      ]
    }
  }'
```

## Examples

### OpenAI SDK with Tags

#### JavaScript Example

```javascript
const OpenAI = require('openai');

class LiteLLMService {
    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.LITELLM_API_KEY,
            baseURL: 'https://ai.gateway.equalcollective.com/v1'
        });
    }

    async chatWithTags(messages, options = {}) {
        const requestBody = {
            model: options.model || 'gpt-4o',
            messages: messages,
            max_tokens: options.maxTokens || 1000,
            temperature: options.temperature || 0.7,
            user: options.user || 'default_user',
            metadata: {
                tags: [
                    `requestId:${this.generateRequestId()}`,
                    `service:${options.service || 'SellerBot'}`,
                    `function:${options.functionName || 'chatWithTags'}`,
                    `useCase:${options.useCase || 'general'}`,
                    `environment:${process.env.NODE_ENV || 'development'}`,
                    ...options.customTags || []
                ]
            }
        };

        try {
            const completion = await this.client.chat.completions.create(requestBody);
            return {
                success: true,
                message: completion.choices[0].message.content,
                usage: completion.usage,
                metadata: requestBody.metadata
            };
        } catch (error) {
            console.error('LiteLLM request failed:', error);
            throw error;
        }
    }

    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// Usage example
const litellm = new LiteLLMService();

async function analyzeSellerData() {
    const result = await litellm.chatWithTags([
        { role: 'system', content: 'You are an Amazon seller data analyst.' },
        { role: 'user', content: 'Analyze this seller performance data...' }
    ], {
        service: 'SellerBot',
        functionName: 'analyzeSellerData',
        useCase: 'seller_analytics',
        user: 'analyst_user_123',
        customTags: [
            'jobID:seller_analysis_001',
            'taskName:performance_analysis',
            'priority:high'
        ]
    });

    console.log('Analysis result:', result.message);
    console.log('Token usage:', result.usage);
}
```

#### Python Example

```python
from openai import OpenAI
import os
import time
import random
import string

class LiteLLMService:
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('LITELLM_API_KEY'),
            base_url='https://ai.gateway.equalcollective.com/v1'
        )

    def chat_with_tags(self, messages, **options):
        request_body = {
            'model': options.get('model', 'gpt-4o'),
            'messages': messages,
            'max_tokens': options.get('max_tokens', 1000),
            'temperature': options.get('temperature', 0.7),
            'user': options.get('user', 'default_user'),
            'metadata': {
                'tags': [
                    f"requestId:{self.generate_request_id()}",
                    f"service:{options.get('service', 'SellerBot')}",
                    f"function:{options.get('function_name', 'chat_with_tags')}",
                    f"useCase:{options.get('use_case', 'general')}",
                    f"environment:{os.getenv('NODE_ENV', 'development')}",
                    *options.get('custom_tags', [])
                ]
            }
        }

        try:
            completion = self.client.chat.completions.create(**request_body)
            return {
                'success': True,
                'message': completion.choices[0].message.content,
                'usage': completion.usage,
                'metadata': request_body['metadata']
            }
        except Exception as error:
            print(f'LiteLLM request failed: {error}')
            raise error

    def generate_request_id(self):
        timestamp = int(time.time())
        random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=9))
        return f"req_{timestamp}_{random_str}"

# Usage example
litellm = LiteLLMService()

def analyze_seller_data():
    result = litellm.chat_with_tags([
        {'role': 'system', 'content': 'You are an Amazon seller data analyst.'},
        {'role': 'user', 'content': 'Analyze this seller performance data...'}
    ],
    service='SellerBot',
    function_name='analyze_seller_data',
    use_case='seller_analytics',
    user='analyst_user_123',
    custom_tags=[
        'jobID:seller_analysis_001',
        'taskName:performance_analysis',
        'priority:high'
    ])

    print(f"Analysis result: {result['message']}")
    print(f"Token usage: {result['usage']}")
```

### Without Tags (Simple Usage)

#### JavaScript - Basic Usage

```javascript
const OpenAI = require('openai');

const client = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1'
});

async function simpleChat() {
    const completion = await client.chat.completions.create({
        model: 'gpt-4o',
        messages: [
            { role: 'user', content: 'What is 2+2?' }
        ]
    });

    return completion.choices[0].message.content;
}
```

#### curl - Basic Usage

```bash
curl -X POST "https://ai.gateway.equalcollective.com/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LITELLM_API_KEY" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "user", "content": "What is 2+2?"}
    ]
  }'
```
