# LiteLLM Test Script Guide

This guide explains how to use the comprehensive LiteLLM test script that validates OpenAI SDK integration with the LiteLLM gateway using proper tagging.

## Quick Start

### 1. Set Up Environment

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and set your API key
# LITELLM_API_KEY=sk-your-persona-key-here
```

### 2. Run the Test

**Option A: Using npm script**
```bash
npm run test:litellm
```

**Option B: Using the shell script (Linux/Mac)**
```bash
./run_litellm_test.sh
```

**Option C: Using the batch file (Windows)**
```cmd
run_litellm_test.bat
```

**Option D: Direct execution**
```bash
node test_litellm_openai_sdk.js
```

## What the Test Script Does

The `test_litellm_openai_sdk.js` script performs comprehensive testing of:

### 🔧 **Configuration**
- **Base URL**: `https://ai.gateway.equalcollective.com/v1`
- **API Key**: From `LITELLM_API_KEY` environment variable
- **OpenAI SDK**: Uses official OpenAI JavaScript library

### 🧪 **Test Cases**

#### 1. **Basic Chat Completion with Tags**
```javascript
{
    model: 'gpt-4o',
    messages: [/* chat messages */],
    user: 'test_user_001',
    metadata: {
        tags: [
            'test:basic_chat',
            'service:TestScript',
            'function:testBasicChatWithTags',
            'useCase:sdk_testing',
            'environment:development',
            'priority:high',
            'requestType:chat_completion'
        ]
    }
}
```

#### 2. **Streaming Response with Tags**
- Tests real-time streaming capabilities
- Validates chunk counting and response assembly
- Includes streaming-specific tags

#### 3. **Multiple Models with Different Tags**
- Tests `gpt-4o`, `gpt-3.5-turbo`, `claude-3-sonnet-********`
- Each model gets unique tags for tracking
- Validates model availability and routing

#### 4. **Complex Tags for Cost Tracking**
- Demonstrates comprehensive tagging strategy
- Includes 20+ different tag categories:
  - Service identification
  - Business context
  - Technical context
  - Custom tracking
  - Cost center information

#### 5. **Error Handling with Tags**
- Tests error scenarios with proper tagging
- Validates error responses and handling
- Ensures tags are sent even during failures

#### 6. **Health Check**
- Tests gateway health endpoint
- Validates models endpoint
- Checks overall system status

### 📊 **Test Output Example**

```
🚀 Starting LiteLLM OpenAI SDK Tests with Tags
📍 Base URL: https://ai.gateway.equalcollective.com/v1
🔑 API Key: sk-abc123...

============================================================
🧪 Gateway Health Check
============================================================
✅ Gateway Health: PASSED (150ms)
✅ Models Endpoint: PASSED (200ms)
   📋 Available models: gpt-4o, gpt-3.5-turbo, claude-3-sonnet-********

============================================================
🧪 Basic Chat Completion with Tags
============================================================
✅ Basic Chat with Tags: PASSED (1250ms)
   Response: "LiteLLM OpenAI SDK test successful!"
   📊 Token Usage: 25 total (15 prompt + 10 completion)

============================================================
🧪 Test Summary
============================================================
📊 Total Tests: 6
✅ Passed: 6
❌ Failed: 0
⏱️  Total Duration: 5420ms

🎉 All tests passed! LiteLLM OpenAI SDK integration is working perfectly.
```

## Key Features Tested

### ✅ **OpenAI SDK Integration**
- Drop-in replacement for OpenAI API
- Full compatibility with existing OpenAI code
- Proper error handling and response parsing

### ✅ **Tagging System**
- Cost tracking by service/function/user
- Business context tagging
- Technical metadata
- Custom tracking fields

### ✅ **Multiple Providers**
- OpenAI models (GPT-4o, GPT-3.5-turbo)
- Anthropic models (Claude-3-Sonnet)
- Automatic routing and load balancing

### ✅ **Streaming Support**
- Real-time response streaming
- Chunk-by-chunk processing
- Proper stream handling

### ✅ **Error Handling**
- Graceful error handling
- Detailed error reporting
- Fallback mechanisms

## Environment Variables

```bash
# Required
LITELLM_API_KEY=sk-your-persona-key-here

# Optional
LITELLM_PROXY_URL=https://ai.gateway.equalcollective.com
LITELLM_DEFAULT_MODEL=gpt-4o
NODE_ENV=development
```

## Troubleshooting

### Common Issues

1. **"API key not set"**
   ```bash
   # Set your API key in .env file
   echo "LITELLM_API_KEY=sk-your-key" >> .env
   ```

2. **"Gateway unreachable"**
   ```bash
   # Test connectivity
   curl https://ai.gateway.equalcollective.com/health
   ```

3. **"Model not found"**
   - Check available models in test output
   - Use fallback models like `gpt-3.5-turbo`

4. **"Package not found"**
   ```bash
   # Install required packages
   npm install openai dotenv
   ```

## Integration Examples

After running the test successfully, you can integrate LiteLLM into your services:

### Basic Integration
```javascript
const OpenAI = require('openai');

const client = new OpenAI({
    apiKey: process.env.LITELLM_API_KEY,
    baseURL: 'https://ai.gateway.equalcollective.com/v1'
});

const completion = await client.chat.completions.create({
    model: 'gpt-4o',
    messages: [{ role: 'user', content: 'Hello!' }],
    metadata: {
        tags: ['service:YourService', 'function:yourFunction']
    }
});
```

### Service Class Pattern
```javascript
class YourAIService {
    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.LITELLM_API_KEY,
            baseURL: 'https://ai.gateway.equalcollective.com/v1'
        });
    }

    async chat(messages, tags = []) {
        return await this.client.chat.completions.create({
            model: 'gpt-4o',
            messages: messages,
            metadata: { tags: ['service:YourService', ...tags] }
        });
    }
}
```

## Next Steps

1. ✅ **Run the test** to validate your setup
2. 📖 **Read the documentation**: [docs/LITELLM_INTEGRATION_GUIDE.md](docs/LITELLM_INTEGRATION_GUIDE.md)
3. 🚀 **Try examples**: `node examples/litellm_quick_start.js`
4. 🔧 **Integrate into your services** using the patterns shown
5. 📊 **Set up monitoring** using the tagging system

## Support

- **Documentation**: [docs/LITELLM_INTEGRATION_GUIDE.md](docs/LITELLM_INTEGRATION_GUIDE.md)
- **Examples**: [examples/litellm_quick_start.js](examples/litellm_quick_start.js)
- **Gateway**: [https://ai.gateway.equalcollective.com/](https://ai.gateway.equalcollective.com/)
- **Test Scripts**: `npm run test:litellm`
