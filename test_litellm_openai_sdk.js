#!/usr/bin/env node

/**
 * LiteLLM OpenAI SDK Test Script with Tags
 * 
 * This script tests the LiteLLM gateway using OpenAI SDK with proper tagging
 * for cost tracking and monitoring.
 */

require('dotenv').config();
const OpenAI = require('openai');

// Configuration
const LITELLM_BASE_URL = 'https://ai.gateway.equalcollective.com/v1';
const LITELLM_API_KEY = process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY || 'sk-test-key';

// Initialize OpenAI client with LiteLLM
const client = new OpenAI({
    apiKey: LITELLM_API_KEY,
    baseURL: LITELLM_BASE_URL
});

// Test results tracking
const testResults = {
    passed: 0,
    failed: 0,
    tests: []
};

function printHeader(title) {
    console.log('\n' + '='.repeat(60));
    console.log(`🧪 ${title}`);
    console.log('='.repeat(60));
}

function printResult(testName, success, details = '', duration = 0) {
    const icon = success ? '✅' : '❌';
    const status = success ? 'PASSED' : 'FAILED';
    console.log(`${icon} ${testName}: ${status} ${duration ? `(${duration}ms)` : ''}`);
    if (details) {
        console.log(`   ${details}`);
    }
    
    testResults.tests.push({ name: testName, success, details, duration });
    if (success) testResults.passed++;
    else testResults.failed++;
}

// Available models for this API key
const AVAILABLE_MODELS = ['gpt-4o', 'gemini/gemini-2.5-flash', 'gemini/gemini-2.5-pro'];
const DEFAULT_MODEL = 'gpt-4o';

// Test 1: Basic Chat Completion with Tags
async function testBasicChatWithTags() {
    printHeader('Basic Chat Completion with Tags');
    const startTime = Date.now();

    try {
        const completion = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                {
                    role: 'system',
                    content: 'You are a helpful assistant. Respond concisely.'
                },
                {
                    role: 'user',
                    content: 'Say "LiteLLM OpenAI SDK test successful!" and nothing else.'
                }
            ],
            max_tokens: 50,
            temperature: 0,
            user: 'test_user_001',
            metadata: {
                tags: [
                    'test:basic_chat',
                    'service:TestScript',
                    'function:testBasicChatWithTags',
                    'useCase:sdk_testing',
                    'environment:development',
                    'priority:high',
                    'requestType:chat_completion'
                ]
            }
        });

        const duration = Date.now() - startTime;
        const message = completion.choices[0]?.message?.content;
        const success = message && message.includes('LiteLLM OpenAI SDK test successful');
        
        printResult('Basic Chat with Tags', success, `Response: "${message}"`, duration);
        
        if (completion.usage) {
            console.log(`   📊 Token Usage: ${completion.usage.total_tokens} total (${completion.usage.prompt_tokens} prompt + ${completion.usage.completion_tokens} completion)`);
        }
        
        return success;
    } catch (error) {
        const duration = Date.now() - startTime;
        printResult('Basic Chat with Tags', false, `Error: ${error.message}`, duration);
        return false;
    }
}

// Test 2: Streaming with Tags
async function testStreamingWithTags() {
    printHeader('Streaming Response with Tags');
    const startTime = Date.now();
    
    try {
        const stream = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                {
                    role: 'user',
                    content: 'Count from 1 to 5, each number on a new line.'
                }
            ],
            max_tokens: 50,
            temperature: 0,
            stream: true,
            user: 'test_user_streaming',
            metadata: {
                tags: [
                    'test:streaming',
                    'service:TestScript',
                    'function:testStreamingWithTags',
                    'useCase:streaming_test',
                    'environment:development',
                    'responseType:stream'
                ]
            }
        });

        let fullResponse = '';
        let chunkCount = 0;
        
        console.log('📡 Streaming response:');
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
                process.stdout.write(content);
                fullResponse += content;
                chunkCount++;
            }
        }
        
        const duration = Date.now() - startTime;
        const success = chunkCount > 0 && fullResponse.length > 0;
        
        console.log(''); // New line after streaming
        printResult('Streaming with Tags', success, `Received ${chunkCount} chunks, ${fullResponse.length} characters`, duration);
        
        return success;
    } catch (error) {
        const duration = Date.now() - startTime;
        printResult('Streaming with Tags', false, `Error: ${error.message}`, duration);
        return false;
    }
}

// Test 3: Multiple Models with Different Tags
async function testMultipleModelsWithTags() {
    printHeader('Multiple Models with Different Tags');

    const models = [
        { name: 'gpt-4o', useCase: 'advanced_reasoning', provider: 'openai' },
        { name: 'gemini/gemini-2.5-flash', useCase: 'fast_response', provider: 'google' },
        { name: 'gemini/gemini-2.5-pro', useCase: 'advanced_google', provider: 'google' }
    ];
    
    let successCount = 0;
    
    for (const model of models) {
        const startTime = Date.now();
        
        try {
            const completion = await client.chat.completions.create({
                model: model.name,
                messages: [
                    {
                        role: 'user',
                        content: `Say "Hello from ${model.name}!" and nothing else.`
                    }
                ],
                max_tokens: 1000,
                temperature: 0,
                user: `test_user_${model.name.replace(/[^a-zA-Z0-9]/g, '_')}`,
                metadata: {
                    tags: [
                        `test:multi_model`,
                        `model:${model.name}`,
                        `service:TestScript`,
                        `function:testMultipleModelsWithTags`,
                        `useCase:${model.useCase}`,
                        `environment:development`,
                        `modelType:${model.provider}`,
                    `provider:${model.provider}`
                    ]
                }
            });

            const duration = Date.now() - startTime;
            const message = completion.choices[0]?.message?.content;
            const success = message && message.includes('Hello from');
            
            if (success) successCount++;
            
            printResult(`Model ${model.name}`, success, `Response: "${message}"`, duration);
            
        } catch (error) {
            const duration = Date.now() - startTime;
            printResult(`Model ${model.name}`, false, `Error: ${error.message}`, duration);
        }
    }
    
    const overallSuccess = successCount > 0;
    console.log(`\n📊 Multiple Models Summary: ${successCount}/${models.length} models working`);
    return overallSuccess;
}

// Test 4: Complex Tags for Cost Tracking
async function testComplexTagsForCostTracking() {
    printHeader('Complex Tags for Cost Tracking');
    const startTime = Date.now();
    
    try {
        const completion = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                {
                    role: 'system',
                    content: 'You are an AI assistant for an e-commerce platform.'
                },
                {
                    role: 'user',
                    content: 'Analyze the benefits of using detailed tagging for cost tracking in AI systems.'
                }
            ],
            max_tokens: 150,
            temperature: 0.7,
            user: 'cost_tracking_analyst',
            metadata: {
                tags: [
                    // Service identification
                    'service:SellerBot',
                    'component:CostAnalysis',
                    'function:testComplexTagsForCostTracking',
                    
                    // Business context
                    'useCase:cost_optimization',
                    'department:engineering',
                    'project:ai_integration',
                    'feature:cost_tracking',
                    
                    // Technical context
                    'environment:development',
                    'requestType:analysis',
                    'priority:high',
                    'complexity:complex',
                    
                    // Custom tracking
                    'jobID:cost_analysis_001',
                    'sessionID:test_session_123',
                    'batchID:batch_001',
                    'version:v1.0.0',
                    
                    // Cost center
                    'costCenter:engineering',
                    'budget:development',
                    'billing:hourly'
                ]
            }
        });

        const duration = Date.now() - startTime;
        const message = completion.choices[0]?.message?.content;
        const success = message && message.length > 50;
        
        printResult('Complex Tags for Cost Tracking', success, `Response length: ${message?.length || 0} characters`, duration);
        
        if (success) {
            console.log('   📋 Sample response:', message.substring(0, 100) + '...');
            console.log('   🏷️  Tags sent: 20 custom tags for detailed cost tracking');
        }
        
        return success;
    } catch (error) {
        const duration = Date.now() - startTime;
        printResult('Complex Tags for Cost Tracking', false, `Error: ${error.message}`, duration);
        return false;
    }
}

// Test 5: Error Handling with Tags
async function testErrorHandlingWithTags() {
    printHeader('Error Handling with Tags');
    const startTime = Date.now();

    try {
        // Test with invalid parameters instead of invalid model
        const completion = await client.chat.completions.create({
            model: DEFAULT_MODEL,
            messages: [
                {
                    role: 'user',
                    content: 'This should work but test error handling in other ways'
                }
            ],
            max_tokens: -1, // Invalid parameter to trigger an error
            user: 'error_test_user',
            metadata: {
                tags: [
                    'test:error_handling',
                    'service:TestScript',
                    'function:testErrorHandlingWithTags',
                    'useCase:parameter_validation',
                    'environment:development',
                    'expectedResult:error'
                ]
            }
        });

        // If we get here, the test failed (we expected an error)
        const duration = Date.now() - startTime;
        printResult('Error Handling with Tags', false, 'Expected error but got success', duration);
        return false;

    } catch (error) {
        const duration = Date.now() - startTime;

        // Check for expected parameter validation errors
        const expectedErrorPatterns = [
            'max_tokens',
            'invalid',
            'parameter',
            'badrequest',
            'minimum value',
            'azureexception',
            'validation'
        ];

        const isExpectedError = expectedErrorPatterns.some(pattern =>
            error.message.toLowerCase().includes(pattern)
        );

        if (isExpectedError) {
            printResult('Error Handling with Tags', true, `Expected parameter validation error (LiteLLM working correctly)`, duration);
            console.log(`   🔍 Error details: ${error.message.substring(0, 100)}...`);
            return true;
        }

        printResult('Error Handling with Tags', false, `Unexpected error: ${error.message}`, duration);
        return false;
    }
}

// Test 6: Health Check
async function testHealthCheck() {
    printHeader('Gateway Health Check');
    const startTime = Date.now();
    
    try {
        // Test gateway health endpoint
        const healthResponse = await fetch('https://ai.gateway.equalcollective.com/health/liveliness');
        const healthDuration = Date.now() - startTime;
        const healthSuccess = healthResponse.status === 200;
        
        printResult('Gateway Health', healthSuccess, `Status: ${healthResponse.status}`, healthDuration);
        
        // Test models endpoint
        const modelsStartTime = Date.now();
        const models = await client.models.list();
        const modelsDuration = Date.now() - modelsStartTime;
        const modelsSuccess = models.data && models.data.length > 0;
        
        printResult('Models Endpoint', modelsSuccess, `Found ${models.data?.length || 0} models`, modelsDuration);
        
        if (modelsSuccess) {
            console.log('   📋 Available models:', models.data.slice(0, 5).map(m => m.id).join(', '));
            console.log('   🎯 Using models:', AVAILABLE_MODELS.join(', '));
        }
        
        return healthSuccess && modelsSuccess;
    } catch (error) {
        const duration = Date.now() - startTime;
        printResult('Health Check', false, `Error: ${error.message}`, duration);
        return false;
    }
}

// Test 7: Validate Available Models
async function testAvailableModels() {
    printHeader('Available Models Validation');

    let successCount = 0;

    for (const model of AVAILABLE_MODELS) {
        const startTime = Date.now();

        try {
            const completion = await client.chat.completions.create({
                model: model,
                messages: [
                    {
                        role: 'user',
                        content: 'Say "Hello" in one word only.'
                    }
                ],
                max_tokens: 1000,
                temperature: 0,
                user: `model_test_${model.replace(/[^a-zA-Z0-9]/g, '_')}`,
                metadata: {
                    tags: [
                        'test:model_validation',
                        `model:${model}`,
                        'service:TestScript',
                        'function:testAvailableModels',
                        'useCase:model_access_validation',
                        'environment:development'
                    ]
                }
            });

            const duration = Date.now() - startTime;
            const message = completion.choices[0]?.message?.content;
            const success = message && message.length > 0;

            if (success) successCount++;

            printResult(`Model Access: ${model}`, success, `Response: "${message}"`, duration);

        } catch (error) {
            const duration = Date.now() - startTime;
            printResult(`Model Access: ${model}`, false, `Error: ${error.message}`, duration);
        }
    }

    const overallSuccess = successCount === AVAILABLE_MODELS.length;
    console.log(`\n📊 Model Access Summary: ${successCount}/${AVAILABLE_MODELS.length} models accessible`);
    return overallSuccess;
}

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting LiteLLM OpenAI SDK Tests with Tags');
    console.log(`📍 Base URL: ${LITELLM_BASE_URL}`);
    console.log(`🔑 API Key: ${LITELLM_API_KEY ? LITELLM_API_KEY.substring(0, 10) + '...' : 'NOT SET'}`);
    
    if (!LITELLM_API_KEY || LITELLM_API_KEY === 'sk-test-key') {
        console.log('\n⚠️  Warning: Please set LITELLM_API_KEY environment variable');
        console.log('   Example: export LITELLM_API_KEY=sk-your-actual-key');
    }
    
    const startTime = Date.now();
    
    // Run all tests
    await testHealthCheck();
    await testAvailableModels();
    await testBasicChatWithTags();
    await testStreamingWithTags();
    await testMultipleModelsWithTags();
    await testComplexTagsForCostTracking();
    await testErrorHandlingWithTags();
    
    const totalDuration = Date.now() - startTime;
    
    // Print summary
    printHeader('Test Summary');
    console.log(`📊 Total Tests: ${testResults.passed + testResults.failed}`);
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All tests passed! LiteLLM OpenAI SDK integration is working perfectly.');
        console.log('\n💡 Key Features Tested:');
        console.log('   ✅ Gateway health monitoring');
        console.log('   ✅ Available models validation');
        console.log('   ✅ Basic chat completions with tags');
        console.log('   ✅ Streaming responses with metadata');
        console.log('   ✅ Multiple model support (GPT-4o, Gemini 2.5 Flash/Pro)');
        console.log('   ✅ Complex tagging for cost tracking');
        console.log('   ✅ Error handling with proper tagging');
    } else {
        console.log('\n⚠️  Some tests failed. Check the configuration and try again.');
    }
    
    return testResults.failed === 0;
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests()
        .then(success => process.exit(success ? 0 : 1))
        .catch(error => {
            console.error('❌ Test suite failed:', error);
            process.exit(1);
        });
}

module.exports = {
    runAllTests,
    testHealthCheck,
    testAvailableModels,
    testBasicChatWithTags,
    testStreamingWithTags,
    testMultipleModelsWithTags,
    testComplexTagsForCostTracking,
    testErrorHandlingWithTags,
    AVAILABLE_MODELS,
    DEFAULT_MODEL
};
